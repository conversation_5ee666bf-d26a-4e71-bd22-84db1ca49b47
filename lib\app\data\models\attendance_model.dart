import 'package:json_annotation/json_annotation.dart';

part 'attendance_model.g.dart';

enum AttendanceStatus {
  @JsonValue('present')
  present,
  @JsonValue('absent')
  absent,
  @JsonValue('late')
  late,
  @JsonValue('excused')
  excused,
}

@JsonSerializable()
class AttendanceRecord {
  final String id;
  final DateTime date;
  final AttendanceStatus status;
  final String? reason;
  final String? subject;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;

  const AttendanceRecord({
    required this.id,
    required this.date,
    required this.status,
    this.reason,
    this.subject,
    this.checkInTime,
    this.checkOutTime,
  });

  factory AttendanceRecord.fromJson(Map<String, dynamic> json) => 
      _$AttendanceRecordFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceRecordToJson(this);
}

@JsonSerializable()
class AttendanceSummary {
  final int totalDays;
  final int presentDays;
  final int absentDays;
  final int lateDays;
  final int excusedDays;
  final double attendancePercentage;
  final List<AttendanceRecord> recentRecords;
  final DateTime lastUpdated;

  const AttendanceSummary({
    required this.totalDays,
    required this.presentDays,
    required this.absentDays,
    required this.lateDays,
    required this.excusedDays,
    required this.attendancePercentage,
    required this.recentRecords,
    required this.lastUpdated,
  });

  factory AttendanceSummary.fromJson(Map<String, dynamic> json) => 
      _$AttendanceSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceSummaryToJson(this);

  int get totalAbsences => absentDays + lateDays;
  
  bool get hasGoodAttendance => attendancePercentage >= 85.0;
  
  String get attendanceGrade {
    if (attendancePercentage >= 95) return 'Excellent';
    if (attendancePercentage >= 85) return 'Good';
    if (attendancePercentage >= 75) return 'Average';
    return 'Poor';
  }
}
