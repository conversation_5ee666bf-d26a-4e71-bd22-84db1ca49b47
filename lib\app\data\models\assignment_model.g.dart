// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assignment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Assignment _$AssignmentFromJson(Map<String, dynamic> json) => Assignment(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  subjectId: json['subjectId'] as String,
  subjectName: json['subjectName'] as String,
  dueDate: DateTime.parse(json['dueDate'] as String),
  assignedDate: DateTime.parse(json['assignedDate'] as String),
  status: $enumDecode(_$AssignmentStatusEnumMap, json['status']),
  priority: $enumDecode(_$AssignmentPriorityEnumMap, json['priority']),
  maxScore: (json['maxScore'] as num?)?.toDouble(),
  score: (json['score'] as num?)?.toDouble(),
  feedback: json['feedback'] as String?,
  submittedAt: json['submittedAt'] == null
      ? null
      : DateTime.parse(json['submittedAt'] as String),
  attachments:
      (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$AssignmentToJson(Assignment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'subjectId': instance.subjectId,
      'subjectName': instance.subjectName,
      'dueDate': instance.dueDate.toIso8601String(),
      'assignedDate': instance.assignedDate.toIso8601String(),
      'status': _$AssignmentStatusEnumMap[instance.status]!,
      'priority': _$AssignmentPriorityEnumMap[instance.priority]!,
      'maxScore': instance.maxScore,
      'score': instance.score,
      'feedback': instance.feedback,
      'submittedAt': instance.submittedAt?.toIso8601String(),
      'attachments': instance.attachments,
    };

const _$AssignmentStatusEnumMap = {
  AssignmentStatus.pending: 'pending',
  AssignmentStatus.submitted: 'submitted',
  AssignmentStatus.graded: 'graded',
  AssignmentStatus.overdue: 'overdue',
  AssignmentStatus.draft: 'draft',
};

const _$AssignmentPriorityEnumMap = {
  AssignmentPriority.low: 'low',
  AssignmentPriority.medium: 'medium',
  AssignmentPriority.high: 'high',
  AssignmentPriority.urgent: 'urgent',
};

AssignmentSummary _$AssignmentSummaryFromJson(Map<String, dynamic> json) =>
    AssignmentSummary(
      totalAssignments: (json['totalAssignments'] as num).toInt(),
      pendingAssignments: (json['pendingAssignments'] as num).toInt(),
      submittedAssignments: (json['submittedAssignments'] as num).toInt(),
      gradedAssignments: (json['gradedAssignments'] as num).toInt(),
      overdueAssignments: (json['overdueAssignments'] as num).toInt(),
      upcomingAssignments: (json['upcomingAssignments'] as List<dynamic>)
          .map((e) => Assignment.fromJson(e as Map<String, dynamic>))
          .toList(),
      recentlyGraded: (json['recentlyGraded'] as List<dynamic>)
          .map((e) => Assignment.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$AssignmentSummaryToJson(AssignmentSummary instance) =>
    <String, dynamic>{
      'totalAssignments': instance.totalAssignments,
      'pendingAssignments': instance.pendingAssignments,
      'submittedAssignments': instance.submittedAssignments,
      'gradedAssignments': instance.gradedAssignments,
      'overdueAssignments': instance.overdueAssignments,
      'upcomingAssignments': instance.upcomingAssignments,
      'recentlyGraded': instance.recentlyGraded,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
