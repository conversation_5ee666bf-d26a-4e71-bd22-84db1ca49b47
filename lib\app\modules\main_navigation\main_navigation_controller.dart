import 'package:get/get.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';

class MainNavigationController extends GetxController {
  // Start with Dashboard (index 2) as the default tab
  final RxInt currentIndex = 2.obs;
  final RxInt notificationBadgeCount = 3.obs;

  late PersistentTabController persistentTabController;

  @override
  void onInit() {
    super.onInit();
    persistentTabController = PersistentTabController(
      initialIndex: 2,
    ); // Dashboard
  }

  @override
  void onClose() {
    persistentTabController.dispose();
    super.onClose();
  }

  void changeTab(int index) {
    currentIndex.value = index;
    persistentTabController.jumpToTab(index);

    // Clear notification badge when notifications tab is selected
    if (index == 1) {
      // Notifications tab index (now at index 1)
      notificationBadgeCount.value = 0;
    }
  }

  void updateNotificationBadge(int count) {
    notificationBadgeCount.value = count;
  }

  // Helper methods to get tab indices for better maintainability
  static const int walletTabIndex = 0;
  static const int notificationsTabIndex = 1;
  static const int dashboardTabIndex = 2; // Center curve button
  static const int settingsTabIndex = 3;
  static const int profileTabIndex = 4;
}
