import 'package:json_annotation/json_annotation.dart';

part 'grade_model.g.dart';

enum GradeType {
  @JsonValue('assignment')
  assignment,
  @JsonValue('quiz')
  quiz,
  @JsonValue('test')
  test,
  @JsonValue('project')
  project,
  @JsonValue('final')
  finalExam,
  @JsonValue('midterm')
  midterm,
}

@JsonSerializable()
class Grade {
  final String id;
  final String subjectId;
  final String subjectName;
  final String title;
  final double score;
  final double maxScore;
  final GradeType type;
  final DateTime dateGraded;
  final DateTime? dueDate;
  final String? feedback;
  final double weight;

  const Grade({
    required this.id,
    required this.subjectId,
    required this.subjectName,
    required this.title,
    required this.score,
    required this.maxScore,
    required this.type,
    required this.dateGraded,
    this.dueDate,
    this.feedback,
    this.weight = 1.0,
  });

  factory Grade.fromJson(Map<String, dynamic> json) => _$GradeFromJson(json);
  Map<String, dynamic> toJson() => _$GradeToJson(this);

  double get percentage => (score / maxScore) * 100;

  String get letterGrade {
    final percent = percentage;
    if (percent >= 97) return 'A+';
    if (percent >= 93) return 'A';
    if (percent >= 90) return 'A-';
    if (percent >= 87) return 'B+';
    if (percent >= 83) return 'B';
    if (percent >= 80) return 'B-';
    if (percent >= 77) return 'C+';
    if (percent >= 73) return 'C';
    if (percent >= 70) return 'C-';
    if (percent >= 67) return 'D+';
    if (percent >= 65) return 'D';
    return 'F';
  }

  bool get isPassing => percentage >= 65;
}

@JsonSerializable()
class SubjectGrades {
  final String subjectId;
  final String subjectName;
  final List<Grade> grades;
  final double currentAverage;
  final String currentLetterGrade;
  final int creditHours;

  const SubjectGrades({
    required this.subjectId,
    required this.subjectName,
    required this.grades,
    required this.currentAverage,
    required this.currentLetterGrade,
    required this.creditHours,
  });

  factory SubjectGrades.fromJson(Map<String, dynamic> json) =>
      _$SubjectGradesFromJson(json);
  Map<String, dynamic> toJson() => _$SubjectGradesToJson(this);
}

@JsonSerializable()
class AcademicSummary {
  final double overallGPA;
  final String overallLetterGrade;
  final List<SubjectGrades> subjects;
  final int totalCreditHours;
  final DateTime lastUpdated;

  const AcademicSummary({
    required this.overallGPA,
    required this.overallLetterGrade,
    required this.subjects,
    required this.totalCreditHours,
    required this.lastUpdated,
  });

  factory AcademicSummary.fromJson(Map<String, dynamic> json) =>
      _$AcademicSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$AcademicSummaryToJson(this);
}
