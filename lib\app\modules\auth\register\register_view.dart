import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/widgets/animated_background.dart';
import '../../../core/widgets/cards/flat_card.dart';
import '../../../core/widgets/glass_text_field.dart';
import '../../../core/widgets/gradient_button.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/constants/app_constants.dart';
import 'register_controller.dart';

class RegisterView extends GetView<RegisterController> {
  const RegisterView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBackground(
        child: Safe<PERSON>rea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Back Button
                Row(
                  children: [
                    IconButton(
                      onPressed: controller.goToLogin,
                      icon: Icon(
                        Icons.arrow_back_ios_rounded,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Header
                Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: Theme.of(context)
                                  .auroraGradients
                                  ?.primaryAuroraGradient ??
                              [
                                Theme.of(context).colorScheme.primary,
                                Theme.of(context).colorScheme.primaryContainer
                              ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context)
                                .colorScheme
                                .primary
                                .withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.person_add_rounded,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Create Account',
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Join ${AppConstants.appName} and start your journey',
                      style: Theme.of(context).textTheme.bodyLarge,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Registration Form
                FlatCard(
                  padding: const EdgeInsets.all(24),
                  child: Form(
                    key: controller.formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Name Field
                        FlatTextField(
                          controller: controller.nameController,
                          hintText: 'Enter your full name',
                          labelText: 'Full Name',
                          keyboardType: TextInputType.name,
                          textCapitalization: TextCapitalization.words,
                          prefixIcon: const Icon(Icons.person_outline),
                          validator: controller.validateName,
                        ),

                        const SizedBox(height: 16),

                        // Email Field
                        FlatTextField(
                          controller: controller.emailController,
                          hintText: 'Enter your email',
                          labelText: 'Email',
                          keyboardType: TextInputType.emailAddress,
                          prefixIcon: const Icon(Icons.email_outlined),
                          validator: controller.validateEmail,
                        ),

                        const SizedBox(height: 16),

                        // Password Field
                        Obx(() => FlatTextField(
                              controller: controller.passwordController,
                              hintText: 'Enter your password',
                              labelText: 'Password',
                              obscureText: controller.obscurePassword.value,
                              prefixIcon: const Icon(Icons.lock_outline),
                              suffixIcon: IconButton(
                                onPressed: controller.togglePasswordVisibility,
                                icon: Icon(
                                  controller.obscurePassword.value
                                      ? Icons.visibility_outlined
                                      : Icons.visibility_off_outlined,
                                ),
                              ),
                              validator: controller.validatePassword,
                            )),

                        const SizedBox(height: 16),

                        // Confirm Password Field
                        Obx(() => FlatTextField(
                              controller: controller.confirmPasswordController,
                              hintText: 'Confirm your password',
                              labelText: 'Confirm Password',
                              obscureText:
                                  controller.obscureConfirmPassword.value,
                              prefixIcon: const Icon(Icons.lock_outline),
                              suffixIcon: IconButton(
                                onPressed:
                                    controller.toggleConfirmPasswordVisibility,
                                icon: Icon(
                                  controller.obscureConfirmPassword.value
                                      ? Icons.visibility_outlined
                                      : Icons.visibility_off_outlined,
                                ),
                              ),
                              validator: controller.validateConfirmPassword,
                            )),

                        const SizedBox(height: 24),

                        // Terms and Privacy Checkboxes
                        Column(
                          children: [
                            Obx(() => Row(
                                  children: [
                                    Checkbox(
                                      value: controller.agreeToTerms.value,
                                      onChanged: (_) =>
                                          controller.toggleTermsAgreement(),
                                      activeColor:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: controller.toggleTermsAgreement,
                                        child: RichText(
                                          text: TextSpan(
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium
                                                ?.copyWith(),
                                            children: [
                                              const TextSpan(
                                                  text: 'I agree to the '),
                                              WidgetSpan(
                                                child: GestureDetector(
                                                  onTap: controller
                                                      .showTermsOfService,
                                                  child: Text(
                                                    'Terms of Service',
                                                    style: TextStyle(
                                                      color: Theme.of(context)
                                                          .colorScheme
                                                          .primary,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      decoration: TextDecoration
                                                          .underline,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                )),
                            Obx(() => Row(
                                  children: [
                                    Checkbox(
                                      value: controller.agreeToPrivacy.value,
                                      onChanged: (_) =>
                                          controller.togglePrivacyAgreement(),
                                      activeColor:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                    Expanded(
                                      child: GestureDetector(
                                        onTap:
                                            controller.togglePrivacyAgreement,
                                        child: RichText(
                                          text: TextSpan(
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium
                                                ?.copyWith(),
                                            children: [
                                              const TextSpan(
                                                  text: 'I agree to the '),
                                              WidgetSpan(
                                                child: GestureDetector(
                                                  onTap: controller
                                                      .showPrivacyPolicy,
                                                  child: Text(
                                                    'Privacy Policy',
                                                    style: TextStyle(
                                                      color: Theme.of(context)
                                                          .colorScheme
                                                          .primary,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      decoration: TextDecoration
                                                          .underline,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                )),
                          ],
                        ),

                        const SizedBox(height: 24),

                        // Register Button
                        Obx(() => GradientButton(
                              text: 'Create Account',
                              onPressed: controller.register,
                              isLoading: controller.isLoading.value,
                              gradient: Theme.of(context)
                                  .auroraGradients
                                  ?.primaryAuroraGradient,
                            )),

                        const SizedBox(height: 16),

                        // Login Link
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Already have an account? ',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(),
                            ),
                            GestureDetector(
                              onTap: controller.goToLogin,
                              child: Text(
                                'Sign In',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
