// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'grade_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Grade _$GradeFromJson(Map<String, dynamic> json) => Grade(
  id: json['id'] as String,
  subjectId: json['subjectId'] as String,
  subjectName: json['subjectName'] as String,
  title: json['title'] as String,
  score: (json['score'] as num).toDouble(),
  maxScore: (json['maxScore'] as num).toDouble(),
  type: $enumDecode(_$GradeTypeEnumMap, json['type']),
  dateGraded: DateTime.parse(json['dateGraded'] as String),
  dueDate: json['dueDate'] == null
      ? null
      : DateTime.parse(json['dueDate'] as String),
  feedback: json['feedback'] as String?,
  weight: (json['weight'] as num?)?.toDouble() ?? 1.0,
);

Map<String, dynamic> _$GradeToJson(Grade instance) => <String, dynamic>{
  'id': instance.id,
  'subjectId': instance.subjectId,
  'subjectName': instance.subjectName,
  'title': instance.title,
  'score': instance.score,
  'maxScore': instance.maxScore,
  'type': _$GradeTypeEnumMap[instance.type]!,
  'dateGraded': instance.dateGraded.toIso8601String(),
  'dueDate': instance.dueDate?.toIso8601String(),
  'feedback': instance.feedback,
  'weight': instance.weight,
};

const _$GradeTypeEnumMap = {
  GradeType.assignment: 'assignment',
  GradeType.quiz: 'quiz',
  GradeType.test: 'test',
  GradeType.project: 'project',
  GradeType.finalExam: 'final',
  GradeType.midterm: 'midterm',
};

SubjectGrades _$SubjectGradesFromJson(Map<String, dynamic> json) =>
    SubjectGrades(
      subjectId: json['subjectId'] as String,
      subjectName: json['subjectName'] as String,
      grades: (json['grades'] as List<dynamic>)
          .map((e) => Grade.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentAverage: (json['currentAverage'] as num).toDouble(),
      currentLetterGrade: json['currentLetterGrade'] as String,
      creditHours: (json['creditHours'] as num).toInt(),
    );

Map<String, dynamic> _$SubjectGradesToJson(SubjectGrades instance) =>
    <String, dynamic>{
      'subjectId': instance.subjectId,
      'subjectName': instance.subjectName,
      'grades': instance.grades,
      'currentAverage': instance.currentAverage,
      'currentLetterGrade': instance.currentLetterGrade,
      'creditHours': instance.creditHours,
    };

AcademicSummary _$AcademicSummaryFromJson(Map<String, dynamic> json) =>
    AcademicSummary(
      overallGPA: (json['overallGPA'] as num).toDouble(),
      overallLetterGrade: json['overallLetterGrade'] as String,
      subjects: (json['subjects'] as List<dynamic>)
          .map((e) => SubjectGrades.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCreditHours: (json['totalCreditHours'] as num).toInt(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$AcademicSummaryToJson(AcademicSummary instance) =>
    <String, dynamic>{
      'overallGPA': instance.overallGPA,
      'overallLetterGrade': instance.overallLetterGrade,
      'subjects': instance.subjects,
      'totalCreditHours': instance.totalCreditHours,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
