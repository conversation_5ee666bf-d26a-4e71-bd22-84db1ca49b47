import 'package:json_annotation/json_annotation.dart';

part 'assignment_model.g.dart';

enum AssignmentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('submitted')
  submitted,
  @JsonValue('graded')
  graded,
  @JsonValue('overdue')
  overdue,
  @JsonValue('draft')
  draft,
}

enum AssignmentPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

@JsonSerializable()
class Assignment {
  final String id;
  final String title;
  final String description;
  final String subjectId;
  final String subjectName;
  final DateTime dueDate;
  final DateTime assignedDate;
  final AssignmentStatus status;
  final AssignmentPriority priority;
  final double? maxScore;
  final double? score;
  final String? feedback;
  final DateTime? submittedAt;
  final List<String> attachments;

  const Assignment({
    required this.id,
    required this.title,
    required this.description,
    required this.subjectId,
    required this.subjectName,
    required this.dueDate,
    required this.assignedDate,
    required this.status,
    required this.priority,
    this.maxScore,
    this.score,
    this.feedback,
    this.submittedAt,
    this.attachments = const [],
  });

  factory Assignment.fromJson(Map<String, dynamic> json) => 
      _$AssignmentFromJson(json);
  Map<String, dynamic> toJson() => _$AssignmentToJson(this);

  bool get isOverdue => DateTime.now().isAfter(dueDate) && status != AssignmentStatus.submitted;
  
  bool get isDueSoon => dueDate.difference(DateTime.now()).inDays <= 2 && status == AssignmentStatus.pending;
  
  Duration get timeRemaining => dueDate.difference(DateTime.now());
  
  String get timeRemainingText {
    final duration = timeRemaining;
    if (duration.isNegative) return 'Overdue';
    
    if (duration.inDays > 0) {
      return '${duration.inDays} day${duration.inDays == 1 ? '' : 's'} left';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} hour${duration.inHours == 1 ? '' : 's'} left';
    } else {
      return '${duration.inMinutes} minute${duration.inMinutes == 1 ? '' : 's'} left';
    }
  }
}

@JsonSerializable()
class AssignmentSummary {
  final int totalAssignments;
  final int pendingAssignments;
  final int submittedAssignments;
  final int gradedAssignments;
  final int overdueAssignments;
  final List<Assignment> upcomingAssignments;
  final List<Assignment> recentlyGraded;
  final DateTime lastUpdated;

  const AssignmentSummary({
    required this.totalAssignments,
    required this.pendingAssignments,
    required this.submittedAssignments,
    required this.gradedAssignments,
    required this.overdueAssignments,
    required this.upcomingAssignments,
    required this.recentlyGraded,
    required this.lastUpdated,
  });

  factory AssignmentSummary.fromJson(Map<String, dynamic> json) => 
      _$AssignmentSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$AssignmentSummaryToJson(this);

  double get completionRate => totalAssignments > 0 ? (submittedAssignments + gradedAssignments) / totalAssignments : 0.0;
}
