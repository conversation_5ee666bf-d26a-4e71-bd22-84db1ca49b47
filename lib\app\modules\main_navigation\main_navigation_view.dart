import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import '../home/<USER>';
import '../wallet/wallet_view.dart';
import '../notifications/notifications_view.dart';
import '../settings/settings_view.dart';
import '../profile/profile_view.dart';
import 'main_navigation_controller.dart';

class MainNavigationView extends GetView<MainNavigationController> {
  const MainNavigationView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Obx(
      () => PersistentTabView(
        context,
        controller: controller.persistentTabController,
        screens: _buildScreens(),
        items: _navBarItems(theme),
        handleAndroidBackButtonPress: true,
        resizeToAvoidBottomInset: true,
        stateManagement: true,
        hideNavigationBarWhenKeyboardAppears: true,
        popBehaviorOnSelectedNavBarItemPress: PopBehavior.all,
        padding: const EdgeInsets.only(top: 8),
        backgroundColor: theme.colorScheme.surface,
        isVisible: true,
        animationSettings: const NavBarAnimationSettings(
          navBarItemAnimation: ItemAnimationSettings(
            duration: Duration(milliseconds: 400),
            curve: Curves.ease,
          ),
          screenTransitionAnimation: ScreenTransitionAnimationSettings(
            animateTabTransition: true,
            duration: Duration(milliseconds: 200),
            screenTransitionAnimationType: ScreenTransitionAnimationType.fadeIn,
          ),
        ),
        confineToSafeArea: true,
        navBarHeight: kBottomNavigationBarHeight + 10,
        navBarStyle: NavBarStyle.style12,
      ),
    );
  }

  List<Widget> _buildScreens() {
    return [
      const WalletView(),
      const NotificationsView(),
      const EnhancedDashboardView(), // Dashboard
      const SettingsView(),
      const ProfileView(),
    ];
  }

  List<PersistentBottomNavBarItem> _navBarItems(ThemeData theme) {
    return [
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.account_balance_wallet_rounded),
        inactiveIcon: const Icon(Icons.account_balance_wallet_outlined),
        title: "Wallet",
        activeColorPrimary: theme.colorScheme.primary,
        inactiveColorPrimary: theme.colorScheme.onSurface.withValues(
          alpha: 0.6,
        ),
        textStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
      PersistentBottomNavBarItem(
        icon: Stack(
          children: [
            const Icon(Icons.notifications_rounded),
            if (controller.notificationBadgeCount.value > 0)
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 12,
                    minHeight: 12,
                  ),
                  child: Text(
                    controller.notificationBadgeCount.value > 9
                        ? '9+'
                        : controller.notificationBadgeCount.value.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
        inactiveIcon: Stack(
          children: [
            const Icon(Icons.notifications_outlined),
            if (controller.notificationBadgeCount.value > 0)
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 12,
                    minHeight: 12,
                  ),
                  child: Text(
                    controller.notificationBadgeCount.value > 9
                        ? '9+'
                        : controller.notificationBadgeCount.value.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
        title: "Notifications",
        activeColorPrimary: theme.colorScheme.primary,
        inactiveColorPrimary: theme.colorScheme.onSurface.withValues(
          alpha: 0.6,
        ),
        textStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.home_rounded),
        inactiveIcon: const Icon(Icons.home_outlined),
        title: "Dashboard",
        activeColorPrimary: theme.colorScheme.primary,
        inactiveColorPrimary: theme.colorScheme.onSurface.withValues(
          alpha: 0.6,
        ),
        textStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.settings_rounded),
        inactiveIcon: const Icon(Icons.settings_outlined),
        title: "Settings",
        activeColorPrimary: theme.colorScheme.primary,
        inactiveColorPrimary: theme.colorScheme.onSurface.withValues(
          alpha: 0.6,
        ),
        textStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.person_rounded),
        inactiveIcon: const Icon(Icons.person_outlined),
        title: "Profile",
        activeColorPrimary: theme.colorScheme.primary,
        inactiveColorPrimary: theme.colorScheme.onSurface.withValues(
          alpha: 0.6,
        ),
        textStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
    ];
  }
}
