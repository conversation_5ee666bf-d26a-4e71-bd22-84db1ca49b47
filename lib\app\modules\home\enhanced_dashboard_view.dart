import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../core/widgets/cards/flat_card.dart';
import '../../core/widgets/cards/flat_feature_card.dart';
import 'home_controller.dart';

class EnhancedDashboardView extends GetView<HomeController> {
  const EnhancedDashboardView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(
          'School Dashboard',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: false,
        actions: [
          IconButton(
            onPressed: () => _showComingSoon(context, 'Notifications'),
            icon: Icon(
              Icons.notifications_outlined,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          IconButton(
            onPressed: controller.logout,
            icon: Icon(
              Icons.logout_rounded,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return RefreshIndicator(
          onRefresh: controller.refreshDashboard,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Welcome Section
                _buildWelcomeCard(context, theme),

                const SizedBox(height: 24),

                // Wallet Balance Card
                _buildWalletCard(context, theme),

                const SizedBox(height: 24),

                // Academic Overview
                Text(
                  'Academic Overview',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                // Enhanced Stats Grid
                _buildStatsGrid(context, theme),

                const SizedBox(height: 24),

                // Upcoming Events
                Text(
                  'Upcoming Events',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                _buildUpcomingEvents(context, theme),

                const SizedBox(height: 24),

                // Quick Actions
                Text(
                  'Quick Actions',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                _buildQuickActions(context, theme),

                const SizedBox(height: 32),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildWelcomeCard(BuildContext context, ThemeData theme) {
    return FlatCard(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getGreeting(),
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 4),
                Obx(
                  () => Text(
                    controller.userName.value,
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Obx(
                  () => Text(
                    controller.studentClass.value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Today is ${_getCurrentDate()}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primary.withValues(alpha: 0.7),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(35),
            ),
            child: const Icon(
              Icons.school_rounded,
              color: Colors.white,
              size: 35,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletCard(BuildContext context, ThemeData theme) {
    return Obx(() {
      final wallet = controller.walletSummary.value;
      if (wallet == null) return const SizedBox.shrink();

      return FlatCard(
        padding: const EdgeInsets.all(24),
        backgroundColor: theme.colorScheme.primary,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.account_balance_wallet_rounded,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Wallet Balance',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Icon(
                  wallet.netChange >= 0
                      ? Icons.trending_up
                      : Icons.trending_down,
                  color: wallet.netChange >= 0
                      ? Colors.green[300]
                      : Colors.red[300],
                  size: 20,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              '\$${wallet.currentBalance.toStringAsFixed(2)}',
              style: theme.textTheme.headlineLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Text(
                  'This month: ',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                Text(
                  wallet.netChange >= 0
                      ? '+\$${wallet.netChange.toStringAsFixed(2)}'
                      : '-\$${wallet.netChange.abs().toStringAsFixed(2)}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: wallet.netChange >= 0
                        ? Colors.green[300]
                        : Colors.red[300],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildStatsGrid(BuildContext context, ThemeData theme) {
    return Obx(() {
      final attendance = controller.attendanceSummary.value;
      final academic = controller.academicSummary.value;
      final assignments = controller.assignmentSummary.value;

      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  context,
                  'Attendance',
                  attendance?.attendancePercentage.toStringAsFixed(1) ?? '0.0',
                  '%',
                  Icons.check_circle_outline,
                  _getAttendanceColor(attendance?.attendancePercentage ?? 0),
                  subtitle: attendance?.attendanceGrade ?? 'N/A',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  context,
                  'Assignments',
                  '${assignments?.submittedAssignments ?? 0}',
                  '/${assignments?.totalAssignments ?? 0}',
                  Icons.assignment_outlined,
                  Colors.blue,
                  subtitle: 'Completed',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  context,
                  'GPA',
                  academic?.overallGPA.toStringAsFixed(1) ?? '0.0',
                  '/4.0',
                  Icons.grade_outlined,
                  Colors.orange,
                  subtitle: academic?.overallLetterGrade ?? 'N/A',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  context,
                  'Pending',
                  '${assignments?.pendingAssignments ?? 0}',
                  '',
                  Icons.pending_actions_outlined,
                  assignments?.pendingAssignments == 0
                      ? Colors.green
                      : Colors.red,
                  subtitle: 'Tasks',
                ),
              ),
            ],
          ),
        ],
      );
    });
  }

  Widget _buildEnhancedStatCard(
    BuildContext context,
    String title,
    String value,
    String suffix,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    final theme = Theme.of(context);

    return FlatCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              if (subtitle != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              if (suffix.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    suffix,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingEvents(BuildContext context, ThemeData theme) {
    return Obx(() {
      final events = controller.upcomingEvents.take(3).toList();

      if (events.isEmpty) {
        return FlatCard(
          padding: const EdgeInsets.all(24),
          child: Center(
            child: Text(
              'No upcoming events',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ),
        );
      }

      return Column(
        children: events
            .map(
              (event) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: FlatCard(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: event.isImportant
                              ? Colors.red.withValues(alpha: 0.1)
                              : theme.colorScheme.primary.withValues(
                                  alpha: 0.1,
                                ),
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: Icon(
                          _getEventIcon(event.type),
                          color: event.isImportant
                              ? Colors.red
                              : theme.colorScheme.primary,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              event.title,
                              style: theme.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              event.location,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _formatEventDate(event.dateTime),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (event.isImportant)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Important',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.red,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            )
            .toList(),
      );
    });
  }

  Widget _buildQuickActions(BuildContext context, ThemeData theme) {
    return Obx(() {
      final actions = controller.quickActions.take(6).toList();

      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.0,
        ),
        itemCount: actions.length,
        itemBuilder: (context, index) {
          final action = actions[index];
          return FlatFeatureCard(
            title: action.title,
            description: action.description,
            icon: _getActionIcon(action.icon),
            onTap: () => _handleQuickAction(action.id),
          );
        },
      );
    });
  }

  // Helper methods
  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Good Morning,';
    if (hour < 17) return 'Good Afternoon,';
    return 'Good Evening,';
  }

  String _getCurrentDate() {
    return DateFormat('EEEE, MMMM d, y').format(DateTime.now());
  }

  Color _getAttendanceColor(double percentage) {
    if (percentage >= 95) return Colors.green;
    if (percentage >= 85) return Colors.orange;
    return Colors.red;
  }

  IconData _getEventIcon(String type) {
    switch (type.toLowerCase()) {
      case 'academic':
        return Icons.school_outlined;
      case 'sports':
        return Icons.sports_outlined;
      case 'meeting':
        return Icons.people_outline;
      case 'event':
        return Icons.event_outlined;
      default:
        return Icons.calendar_today_outlined;
    }
  }

  IconData _getActionIcon(String iconName) {
    switch (iconName) {
      case 'assignment':
        return Icons.assignment_outlined;
      case 'grade':
        return Icons.grade_outlined;
      case 'schedule':
        return Icons.schedule_outlined;
      case 'library_books':
        return Icons.library_books_outlined;
      case 'restaurant_menu':
        return Icons.restaurant_menu_outlined;
      case 'directions_bus':
        return Icons.directions_bus_outlined;
      default:
        return Icons.apps_outlined;
    }
  }

  String _formatEventDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);

    if (difference.inDays == 0) {
      return 'Today at ${DateFormat('h:mm a').format(dateTime)}';
    } else if (difference.inDays == 1) {
      return 'Tomorrow at ${DateFormat('h:mm a').format(dateTime)}';
    } else if (difference.inDays < 7) {
      return '${DateFormat('EEEE').format(dateTime)} at ${DateFormat('h:mm a').format(dateTime)}';
    } else {
      return DateFormat('MMM d, h:mm a').format(dateTime);
    }
  }

  void _handleQuickAction(String actionId) {
    switch (actionId) {
      case 'assignments':
        controller.navigateToAssignments();
        break;
      case 'grades':
        controller.navigateToGrades();
        break;
      case 'schedule':
        controller.navigateToSchedule();
        break;
      default:
        _showComingSoon(Get.context!, actionId);
    }
  }

  void _showComingSoon(BuildContext context, String feature) {
    Get.snackbar(
      'Coming Soon',
      '$feature feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }
}
