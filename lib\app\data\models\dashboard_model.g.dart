// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuickAction _$QuickActionFromJson(Map<String, dynamic> json) => QuickAction(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  icon: json['icon'] as String,
  route: json['route'] as String,
  isEnabled: json['isEnabled'] as bool? ?? true,
);

Map<String, dynamic> _$QuickActionToJson(QuickAction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'icon': instance.icon,
      'route': instance.route,
      'isEnabled': instance.isEnabled,
    };

UpcomingEvent _$UpcomingEventFromJson(Map<String, dynamic> json) =>
    UpcomingEvent(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      dateTime: DateTime.parse(json['dateTime'] as String),
      location: json['location'] as String,
      type: json['type'] as String,
      isImportant: json['isImportant'] as bool? ?? false,
    );

Map<String, dynamic> _$UpcomingEventToJson(UpcomingEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'dateTime': instance.dateTime.toIso8601String(),
      'location': instance.location,
      'type': instance.type,
      'isImportant': instance.isImportant,
    };

WalletSummary _$WalletSummaryFromJson(Map<String, dynamic> json) =>
    WalletSummary(
      currentBalance: (json['currentBalance'] as num).toDouble(),
      monthlySpending: (json['monthlySpending'] as num).toDouble(),
      monthlyIncome: (json['monthlyIncome'] as num).toDouble(),
      recentTransactions: (json['recentTransactions'] as List<dynamic>)
          .map((e) => Transaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$WalletSummaryToJson(WalletSummary instance) =>
    <String, dynamic>{
      'currentBalance': instance.currentBalance,
      'monthlySpending': instance.monthlySpending,
      'monthlyIncome': instance.monthlyIncome,
      'recentTransactions': instance.recentTransactions,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

DashboardData _$DashboardDataFromJson(Map<String, dynamic> json) =>
    DashboardData(
      studentId: json['studentId'] as String,
      studentName: json['studentName'] as String,
      studentClass: json['studentClass'] as String,
      attendance: AttendanceSummary.fromJson(
        json['attendance'] as Map<String, dynamic>,
      ),
      academics: AcademicSummary.fromJson(
        json['academics'] as Map<String, dynamic>,
      ),
      assignments: AssignmentSummary.fromJson(
        json['assignments'] as Map<String, dynamic>,
      ),
      wallet: WalletSummary.fromJson(json['wallet'] as Map<String, dynamic>),
      quickActions: (json['quickActions'] as List<dynamic>)
          .map((e) => QuickAction.fromJson(e as Map<String, dynamic>))
          .toList(),
      upcomingEvents: (json['upcomingEvents'] as List<dynamic>)
          .map((e) => UpcomingEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$DashboardDataToJson(DashboardData instance) =>
    <String, dynamic>{
      'studentId': instance.studentId,
      'studentName': instance.studentName,
      'studentClass': instance.studentClass,
      'attendance': instance.attendance,
      'academics': instance.academics,
      'assignments': instance.assignments,
      'wallet': instance.wallet,
      'quickActions': instance.quickActions,
      'upcomingEvents': instance.upcomingEvents,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
