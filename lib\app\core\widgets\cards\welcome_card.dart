import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../constants/app_constants.dart';
import 'flat_card.dart';

/// Welcome card component for displaying welcome message with icon
class WelcomeCard extends StatelessWidget {
  const WelcomeCard({
    super.key,
    this.title,
    this.subtitle,
    this.icon = Icons.celebration_rounded,
    this.iconSize = 40.0,
    this.iconContainerSize = 80.0,
    this.gradient,
    this.padding = const EdgeInsets.all(24),
    this.onTap,
  });

  final String? title;
  final String? subtitle;
  final IconData icon;
  final double iconSize;
  final double iconContainerSize;
  final List<Color>? gradient;
  final EdgeInsetsGeometry padding;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final auroraGradients = theme.auroraGradients;
    final effectiveGradient = gradient ??
        auroraGradients?.primaryAuroraGradient ??
        [theme.colorScheme.primary, theme.colorScheme.primaryContainer];
    final effectiveTitle = title ?? 'Welcome to ${AppConstants.appName}!';
    final effectiveSubtitle =
        subtitle ?? 'Discover amazing features and beautiful design';

    return FlatCard(
      padding: padding,
      onTap: onTap,
      child: Column(
        children: [
          // Enhanced icon container with multiple shadows
          Container(
            width: iconContainerSize,
            height: iconContainerSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: effectiveGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: effectiveGradient.first.withValues(alpha: 0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: effectiveGradient.last.withValues(alpha: 0.3),
                  blurRadius: 40,
                  offset: const Offset(0, 20),
                  spreadRadius: 0,
                ),
                // Inner glow effect
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                  spreadRadius: -4,
                ),
              ],
            ),
            child: Icon(
              icon,
              size: iconSize,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 32),

          // Enhanced title with better typography
          Text(
            effectiveTitle,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w700,
              color: theme.colorScheme.onSurface,
              letterSpacing: -0.5,
              height: 1.2,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Enhanced subtitle with improved readability
          Text(
            effectiveSubtitle,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              height: 1.6,
              letterSpacing: 0.1,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
