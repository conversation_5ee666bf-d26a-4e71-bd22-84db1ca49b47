import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/widgets/animated_background.dart';
import '../../../core/widgets/cards/flat_card.dart';
import '../../../core/widgets/glass_text_field.dart';
import '../../../core/widgets/gradient_button.dart';
import '../../../core/theme/app_theme.dart';
import 'change_password_controller.dart';

class ChangePasswordView extends GetView<ChangePasswordController> {
  const ChangePasswordView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: AnimatedBackground(
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: controller.showDiscardDialog,
                      icon: Icon(
                        Icons.arrow_back_ios_rounded,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Change Password',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 48), // Balance the back button
                  ],
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: controller.formKey,
                    child: Column(
                      children: [
                        // Security Info
                        FlatCard(
                          padding: const EdgeInsets.all(20),
                          backgroundColor: Theme.of(context)
                              .colorScheme
                              .primary
                              .withValues(alpha: 0.1),
                          borderColor: Theme.of(context)
                              .colorScheme
                              .primary
                              .withValues(alpha: 0.3),
                          borderWidth: 1,
                          child: Row(
                            children: [
                              Icon(
                                Icons.security,
                                color: Theme.of(context).colorScheme.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'Choose a strong password to keep your account secure.',
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Password Form
                        FlatCard(
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              // Current Password
                              Obx(() => FlatTextField(
                                    controller:
                                        controller.currentPasswordController,
                                    hintText: 'Enter your current password',
                                    labelText: 'Current Password',
                                    obscureText:
                                        controller.obscureCurrentPassword.value,
                                    prefixIcon: const Icon(Icons.lock_outline),
                                    suffixIcon: IconButton(
                                      onPressed: controller
                                          .toggleCurrentPasswordVisibility,
                                      icon: Icon(
                                        controller.obscureCurrentPassword.value
                                            ? Icons.visibility_outlined
                                            : Icons.visibility_off_outlined,
                                      ),
                                    ),
                                    validator:
                                        controller.validateCurrentPassword,
                                  )),

                              const SizedBox(height: 20),

                              // New Password
                              Obx(() => FlatTextField(
                                    controller:
                                        controller.newPasswordController,
                                    hintText: 'Enter your new password',
                                    labelText: 'New Password',
                                    obscureText:
                                        controller.obscureNewPassword.value,
                                    prefixIcon: const Icon(Icons.lock_outline),
                                    suffixIcon: IconButton(
                                      onPressed: controller
                                          .toggleNewPasswordVisibility,
                                      icon: Icon(
                                        controller.obscureNewPassword.value
                                            ? Icons.visibility_outlined
                                            : Icons.visibility_off_outlined,
                                      ),
                                    ),
                                    validator: controller.validateNewPassword,
                                    onChanged: (value) => controller.update(),
                                  )),

                              const SizedBox(height: 20),

                              // Confirm Password
                              Obx(() => FlatTextField(
                                    controller:
                                        controller.confirmPasswordController,
                                    hintText: 'Confirm your new password',
                                    labelText: 'Confirm New Password',
                                    obscureText:
                                        controller.obscureConfirmPassword.value,
                                    prefixIcon: const Icon(Icons.lock_outline),
                                    suffixIcon: IconButton(
                                      onPressed: controller
                                          .toggleConfirmPasswordVisibility,
                                      icon: Icon(
                                        controller.obscureConfirmPassword.value
                                            ? Icons.visibility_outlined
                                            : Icons.visibility_off_outlined,
                                      ),
                                    ),
                                    validator:
                                        controller.validateConfirmPassword,
                                  )),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Password Requirements
                        FlatCard(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Password Requirements',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 12),
                              GetBuilder<ChangePasswordController>(
                                builder: (controller) {
                                  return Column(
                                    children: List.generate(
                                      controller.passwordRequirements.length,
                                      (index) => _buildRequirementItem(
                                        context,
                                        controller.passwordRequirements[index],
                                        controller.isRequirementMet(index),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Change Password Button
                        Obx(() => GradientButton(
                              text: 'Change Password',
                              onPressed: controller.changePassword,
                              isLoading: controller.isLoading.value,
                              gradient: Theme.of(context)
                                  .auroraGradients
                                  ?.primaryAuroraGradient,
                            )),

                        const SizedBox(height: 16),

                        // Cancel Button
                        OutlinedButton(
                          onPressed: controller.showDiscardDialog,
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            side: BorderSide(
                                color: Theme.of(context).colorScheme.outline),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRequirementItem(
      BuildContext context, String requirement, bool isMet) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isMet ? Icons.check_circle : Icons.radio_button_unchecked,
            size: 16,
            color: isMet
                ? Colors.green
                : AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              requirement,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isMet ? Colors.green : AppColors.textSecondary,
                fontWeight: isMet ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
