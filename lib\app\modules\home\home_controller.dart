import 'package:get/get.dart';
import '../../core/services/storage_service.dart';
import '../../routes/app_routes.dart';
import '../../data/models/dashboard_model.dart';
import '../../data/models/attendance_model.dart';
import '../../data/models/grade_model.dart';
import '../../data/models/assignment_model.dart';
import '../../data/models/transaction_model.dart';

class HomeController extends GetxController {
  final RxString userName = 'Alex Johnson'.obs;
  final RxString studentClass = 'Grade 10-A'.obs;
  final RxString studentId = 'STU2024001'.obs;

  // Dashboard data observables
  final Rx<AttendanceSummary?> attendanceSummary = Rx<AttendanceSummary?>(null);
  final Rx<AcademicSummary?> academicSummary = Rx<AcademicSummary?>(null);
  final Rx<AssignmentSummary?> assignmentSummary = Rx<AssignmentSummary?>(null);
  final Rx<WalletSummary?> walletSummary = Rx<WalletSummary?>(null);
  final RxList<QuickAction> quickActions = <QuickAction>[].obs;
  final RxList<UpcomingEvent> upcomingEvents = <UpcomingEvent>[].obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadUserData();
    _loadDashboardData();
  }

  void _loadUserData() {
    final userData = StorageService.to.userData;
    if (userData != null && userData['name'] != null) {
      userName.value = userData['name'];
    }
  }

  void _loadDashboardData() {
    isLoading.value = true;

    // Simulate loading delay
    Future.delayed(const Duration(milliseconds: 800), () {
      _loadAttendanceData();
      _loadAcademicData();
      _loadAssignmentData();
      _loadWalletData();
      _loadQuickActions();
      _loadUpcomingEvents();
      isLoading.value = false;
    });
  }

  void _loadAttendanceData() {
    final now = DateTime.now();
    final recentRecords = [
      AttendanceRecord(
        id: '1',
        date: now.subtract(const Duration(days: 1)),
        status: AttendanceStatus.present,
        checkInTime: DateTime(now.year, now.month, now.day - 1, 8, 15),
        checkOutTime: DateTime(now.year, now.month, now.day - 1, 15, 30),
      ),
      AttendanceRecord(
        id: '2',
        date: now.subtract(const Duration(days: 2)),
        status: AttendanceStatus.late,
        reason: 'Traffic delay',
        checkInTime: DateTime(now.year, now.month, now.day - 2, 8, 45),
        checkOutTime: DateTime(now.year, now.month, now.day - 2, 15, 30),
      ),
      AttendanceRecord(
        id: '3',
        date: now.subtract(const Duration(days: 3)),
        status: AttendanceStatus.present,
        checkInTime: DateTime(now.year, now.month, now.day - 3, 8, 10),
        checkOutTime: DateTime(now.year, now.month, now.day - 3, 15, 30),
      ),
    ];

    attendanceSummary.value = AttendanceSummary(
      totalDays: 45,
      presentDays: 41,
      absentDays: 2,
      lateDays: 2,
      excusedDays: 0,
      attendancePercentage: 91.1,
      recentRecords: recentRecords,
      lastUpdated: now,
    );
  }

  void _loadAcademicData() {
    final subjects = [
      SubjectGrades(
        subjectId: 'math',
        subjectName: 'Mathematics',
        grades: [
          Grade(
            id: '1',
            subjectId: 'math',
            subjectName: 'Mathematics',
            title: 'Algebra Test',
            score: 87,
            maxScore: 100,
            type: GradeType.test,
            dateGraded: DateTime.now().subtract(const Duration(days: 5)),
          ),
          Grade(
            id: '2',
            subjectId: 'math',
            subjectName: 'Mathematics',
            title: 'Homework Assignment',
            score: 95,
            maxScore: 100,
            type: GradeType.assignment,
            dateGraded: DateTime.now().subtract(const Duration(days: 2)),
          ),
        ],
        currentAverage: 91.0,
        currentLetterGrade: 'A-',
        creditHours: 4,
      ),
      SubjectGrades(
        subjectId: 'science',
        subjectName: 'Physics',
        grades: [
          Grade(
            id: '3',
            subjectId: 'science',
            subjectName: 'Physics',
            title: 'Lab Report',
            score: 92,
            maxScore: 100,
            type: GradeType.project,
            dateGraded: DateTime.now().subtract(const Duration(days: 3)),
          ),
        ],
        currentAverage: 92.0,
        currentLetterGrade: 'A-',
        creditHours: 3,
      ),
    ];

    academicSummary.value = AcademicSummary(
      overallGPA: 3.7,
      overallLetterGrade: 'A-',
      subjects: subjects,
      totalCreditHours: 24,
      lastUpdated: DateTime.now(),
    );
  }

  void _loadAssignmentData() {
    final now = DateTime.now();
    final upcomingAssignments = [
      Assignment(
        id: '1',
        title: 'Physics Lab Report',
        description: 'Complete the pendulum experiment analysis',
        subjectId: 'physics',
        subjectName: 'Physics',
        dueDate: now.add(const Duration(days: 3)),
        assignedDate: now.subtract(const Duration(days: 7)),
        status: AssignmentStatus.pending,
        priority: AssignmentPriority.high,
        maxScore: 100,
      ),
      Assignment(
        id: '2',
        title: 'Math Problem Set',
        description: 'Chapter 5 exercises 1-20',
        subjectId: 'math',
        subjectName: 'Mathematics',
        dueDate: now.add(const Duration(days: 1)),
        assignedDate: now.subtract(const Duration(days: 3)),
        status: AssignmentStatus.pending,
        priority: AssignmentPriority.urgent,
        maxScore: 50,
      ),
    ];

    final recentlyGraded = [
      Assignment(
        id: '3',
        title: 'History Essay',
        description: 'World War II analysis',
        subjectId: 'history',
        subjectName: 'History',
        dueDate: now.subtract(const Duration(days: 5)),
        assignedDate: now.subtract(const Duration(days: 12)),
        status: AssignmentStatus.graded,
        priority: AssignmentPriority.medium,
        maxScore: 100,
        score: 88,
        feedback: 'Excellent analysis with good supporting evidence.',
        submittedAt: now.subtract(const Duration(days: 6)),
      ),
    ];

    assignmentSummary.value = AssignmentSummary(
      totalAssignments: 15,
      pendingAssignments: 3,
      submittedAssignments: 8,
      gradedAssignments: 4,
      overdueAssignments: 0,
      upcomingAssignments: upcomingAssignments,
      recentlyGraded: recentlyGraded,
      lastUpdated: now,
    );
  }

  void _loadWalletData() {
    final now = DateTime.now();
    final recentTransactions = [
      Transaction(
        id: '1',
        title: 'Cafeteria Lunch',
        amount: -12.50,
        date: now.subtract(const Duration(hours: 3)),
        type: TransactionType.expense,
        category: 'Food',
        icon: 'restaurant',
        description: 'Daily lunch meal',
      ),
      Transaction(
        id: '2',
        title: 'Weekly Allowance',
        amount: 50.00,
        date: now.subtract(const Duration(days: 2)),
        type: TransactionType.income,
        category: 'Allowance',
        icon: 'account_balance_wallet',
        description: 'Weekly pocket money',
      ),
      Transaction(
        id: '3',
        title: 'Library Fine',
        amount: -3.00,
        date: now.subtract(const Duration(days: 4)),
        type: TransactionType.expense,
        category: 'Library',
        icon: 'book',
        description: 'Late return penalty',
      ),
    ];

    walletSummary.value = WalletSummary(
      currentBalance: 127.50,
      monthlySpending: 85.30,
      monthlyIncome: 200.00,
      recentTransactions: recentTransactions,
      lastUpdated: now,
    );
  }

  void _loadQuickActions() {
    quickActions.value = [
      const QuickAction(
        id: 'assignments',
        title: 'View Assignments',
        description: 'Check pending assignments',
        icon: 'assignment',
        route: '/assignments',
      ),
      const QuickAction(
        id: 'grades',
        title: 'Check Grades',
        description: 'View recent grades',
        icon: 'grade',
        route: '/grades',
      ),
      const QuickAction(
        id: 'schedule',
        title: 'Class Schedule',
        description: 'Today\'s timetable',
        icon: 'schedule',
        route: '/schedule',
      ),
      const QuickAction(
        id: 'library',
        title: 'Library',
        description: 'Browse books & resources',
        icon: 'library_books',
        route: '/library',
      ),
      const QuickAction(
        id: 'cafeteria',
        title: 'Cafeteria Menu',
        description: 'Today\'s meal options',
        icon: 'restaurant_menu',
        route: '/cafeteria',
      ),
      const QuickAction(
        id: 'transport',
        title: 'School Bus',
        description: 'Bus routes & timings',
        icon: 'directions_bus',
        route: '/transport',
      ),
    ];
  }

  void _loadUpcomingEvents() {
    final now = DateTime.now();
    upcomingEvents.value = [
      UpcomingEvent(
        id: '1',
        title: 'Math Quiz',
        description: 'Chapter 5: Quadratic Equations',
        dateTime: now.add(const Duration(days: 1, hours: 10)),
        location: 'Room 201',
        type: 'Academic',
        isImportant: true,
      ),
      UpcomingEvent(
        id: '2',
        title: 'Science Fair',
        description: 'Annual science project exhibition',
        dateTime: now.add(const Duration(days: 5, hours: 9)),
        location: 'Main Auditorium',
        type: 'Event',
        isImportant: false,
      ),
      UpcomingEvent(
        id: '3',
        title: 'Parent-Teacher Meeting',
        description: 'Quarterly progress discussion',
        dateTime: now.add(const Duration(days: 7, hours: 14)),
        location: 'Conference Hall',
        type: 'Meeting',
        isImportant: true,
      ),
      UpcomingEvent(
        id: '4',
        title: 'Sports Day Practice',
        description: 'Track and field preparation',
        dateTime: now.add(const Duration(hours: 26)),
        location: 'Sports Ground',
        type: 'Sports',
        isImportant: false,
      ),
    ];
  }

  // Refresh dashboard data
  Future<void> refreshDashboard() async {
    _loadDashboardData();
  }

  // Navigation helpers
  void navigateToAssignments() {
    Get.snackbar(
      'Coming Soon',
      'Assignments page will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void navigateToGrades() {
    Get.snackbar(
      'Coming Soon',
      'Grades page will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void navigateToSchedule() {
    Get.snackbar(
      'Coming Soon',
      'Schedule page will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  Future<void> logout() async {
    await StorageService.to.clearUserData();
    Get.offAllNamed(AppRoutes.login);
  }
}
