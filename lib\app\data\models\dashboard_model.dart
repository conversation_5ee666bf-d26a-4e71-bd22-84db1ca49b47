import 'package:json_annotation/json_annotation.dart';
import 'attendance_model.dart';
import 'grade_model.dart';
import 'assignment_model.dart';
import 'transaction_model.dart';

part 'dashboard_model.g.dart';

@JsonSerializable()
class QuickAction {
  final String id;
  final String title;
  final String description;
  final String icon;
  final String route;
  final bool isEnabled;

  const QuickAction({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.route,
    this.isEnabled = true,
  });

  factory QuickAction.fromJson(Map<String, dynamic> json) => 
      _$QuickActionFromJson(json);
  Map<String, dynamic> toJson() => _$QuickActionToJson(this);
}

@JsonSerializable()
class UpcomingEvent {
  final String id;
  final String title;
  final String description;
  final DateTime dateTime;
  final String location;
  final String type;
  final bool isImportant;

  const UpcomingEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.dateTime,
    required this.location,
    required this.type,
    this.isImportant = false,
  });

  factory UpcomingEvent.fromJson(Map<String, dynamic> json) => 
      _$UpcomingEventFromJson(json);
  Map<String, dynamic> toJson() => _$UpcomingEventToJson(this);

  bool get isToday {
    final now = DateTime.now();
    return dateTime.year == now.year && 
           dateTime.month == now.month && 
           dateTime.day == now.day;
  }

  bool get isTomorrow {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return dateTime.year == tomorrow.year && 
           dateTime.month == tomorrow.month && 
           dateTime.day == tomorrow.day;
  }
}

@JsonSerializable()
class WalletSummary {
  final double currentBalance;
  final double monthlySpending;
  final double monthlyIncome;
  final List<Transaction> recentTransactions;
  final DateTime lastUpdated;

  const WalletSummary({
    required this.currentBalance,
    required this.monthlySpending,
    required this.monthlyIncome,
    required this.recentTransactions,
    required this.lastUpdated,
  });

  factory WalletSummary.fromJson(Map<String, dynamic> json) => 
      _$WalletSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$WalletSummaryToJson(this);

  double get netChange => monthlyIncome - monthlySpending;
}

@JsonSerializable()
class DashboardData {
  final String studentId;
  final String studentName;
  final String studentClass;
  final AttendanceSummary attendance;
  final AcademicSummary academics;
  final AssignmentSummary assignments;
  final WalletSummary wallet;
  final List<QuickAction> quickActions;
  final List<UpcomingEvent> upcomingEvents;
  final DateTime lastUpdated;

  const DashboardData({
    required this.studentId,
    required this.studentName,
    required this.studentClass,
    required this.attendance,
    required this.academics,
    required this.assignments,
    required this.wallet,
    required this.quickActions,
    required this.upcomingEvents,
    required this.lastUpdated,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) => 
      _$DashboardDataFromJson(json);
  Map<String, dynamic> toJson() => _$DashboardDataToJson(this);
}
